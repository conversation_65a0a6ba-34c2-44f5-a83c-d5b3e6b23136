<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AiDataExchangeMakerPartNoMapper">

    <resultMap type="AiDataExchangeMakerPartNo" id="AiDataExchangeMakerPartNoResult">
        <result property="aiDataExchangeMakerPartNoId"    column="ai_data_exchange_maker_part_no_id"    />
        <result property="makerPartNo"    column="maker_part_no"    />
    </resultMap>

    <sql id="selectAiDataExchangeMakerPartNoVo">
        select ai_data_exchange_maker_part_no_id, maker_part_no from ai_data_exchange_maker_part_no
    </sql>

    <select id="selectAiDataExchangeMakerPartNoList" parameterType="AiDataExchangeMakerPartNo" resultMap="AiDataExchangeMakerPartNoResult">
        <include refid="selectAiDataExchangeMakerPartNoVo"/>
        <where>
            <if test="aiDataExchangeMakerPartNoId != null "> and ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}</if>
            <if test="makerPartNo != null  and makerPartNo != ''"> and UPPER(REPLACE(maker_part_no, ' ', '')) = #{makerPartNo}</if>
        </where>
    </select>

    <select id="selectAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoId" parameterType="Long" resultMap="AiDataExchangeMakerPartNoResult">
        <include refid="selectAiDataExchangeMakerPartNoVo"/>
        where ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}
    </select>

    <insert id="insertAiDataExchangeMakerPartNo" parameterType="AiDataExchangeMakerPartNo" useGeneratedKeys="true" keyProperty="aiDataExchangeMakerPartNoId">
        insert into ai_data_exchange_maker_part_no
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeMakerPartNoId != null">ai_data_exchange_maker_part_no_id,</if>
            <if test="makerPartNo != null">maker_part_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeMakerPartNoId != null">#{aiDataExchangeMakerPartNoId},</if>
            <if test="makerPartNo != null">#{makerPartNo},</if>
         </trim>
    </insert>

    <update id="updateAiDataExchangeMakerPartNo" parameterType="AiDataExchangeMakerPartNo">
        update ai_data_exchange_maker_part_no
        <trim prefix="SET" suffixOverrides=",">
            <if test="makerPartNo != null">maker_part_no = #{makerPartNo},</if>
        </trim>
        where ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}
    </update>

    <delete id="deleteAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoId" parameterType="Long">
        delete from ai_data_exchange_maker_part_no where ai_data_exchange_maker_part_no_id = #{aiDataExchangeMakerPartNoId}
    </delete>

    <delete id="deleteAiDataExchangeMakerPartNoByAiDataExchangeMakerPartNoIds" parameterType="String">
        delete from ai_data_exchange_maker_part_no where ai_data_exchange_maker_part_no_id in
        <foreach item="aiDataExchangeMakerPartNoId" collection="array" open="(" separator="," close=")">
            #{aiDataExchangeMakerPartNoId}
        </foreach>
    </delete>
</mapper>
