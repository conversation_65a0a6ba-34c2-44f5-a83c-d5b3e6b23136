package com.ruoyi.system.utils;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.InspectionItemsSet;
import com.ruoyi.system.domain.mitac.MiTACQualityReportRequest;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Excel解析工具类
 */
public class ExcelParser {
    private static final Logger log = LoggerFactory.getLogger(ExcelParser.class);

    // 数据开始的行索引，默认为1（表示从第2行开始，因为有表头）
    private static int startRow = 1;

    /**
     * 解析Excel文件为质检报告请求
     *
     * @param file Excel文件
     * @return 质检报告请求
     */
    public static MiTACQualityReportRequest parseExcel(MultipartFile file) {
        try (InputStream is = file.getInputStream()) {
            Workbook workbook = WorkbookFactory.create(is);

            // 创建质检报告请求对象
            MiTACQualityReportRequest request = new MiTACQualityReportRequest();

            // 获取第一个Sheet
            Sheet sheet = workbook.getSheetAt(0);

            // 检测空白行并设置startRow
            detectEmptyRowsAndSetStartRow(sheet);

            // 解析基本信息（假设在第一个Sheet的前几行）
            parseBasicInfo(sheet, request);

            // 检查产品类别是否为空
            String category = request.getCategory();
            if (category == null || category.trim().isEmpty()) {
                // 如果产品类别为空，直接抛出异常
                log.error("产品类别为空");
                throw new RuntimeException("产品类别不能为空");
            }

            // 使用通用的解析检验项目方法
            parseInspectionItems(sheet, request);

            return request;
        } catch (Exception e) {
            log.error("解析Excel文件异常", e);
            throw new RuntimeException("解析Excel文件异常: " + e.getMessage());
        }
    }

    /**
     * 从本地文件解析Excel文件为质检报告请求
     *
     * @param filePath Excel文件路径
     * @return 质检报告请求
     */
    public static MiTACQualityReportRequest parseExcelFromFile(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook = WorkbookFactory.create(fis);

            // 创建质检报告请求对象
            MiTACQualityReportRequest request = new MiTACQualityReportRequest();

            // 获取第一个Sheet
            Sheet sheet = workbook.getSheetAt(0);

            // 检测空白行并设置startRow
            detectEmptyRowsAndSetStartRow(sheet);

            // 解析基本信息（假设在第一个Sheet的前几行）
            parseBasicInfo(sheet, request);

            // 检查产品类别是否为空
            String category = request.getCategory();
            if (category == null || category.trim().isEmpty()) {
                // 如果产品类别为空，直接抛出异常
                log.error("产品类别为空");
                throw new RuntimeException("产品类别不能为空");
            }

            // 使用通用的解析检验项目方法
            parseInspectionItems(sheet, request);

            return request;
        } catch (Exception e) {
            log.error("解析Excel文件异常: {}", filePath, e);
            throw new RuntimeException("解析Excel文件异常: " + e.getMessage());
        }
    }

    /**
     * 从本地文件解析Excel文件为质检报告请求
     *
     * @param file Excel文件
     * @return 质检报告请求
     */
    public static MiTACQualityReportRequest parseExcelFromFile(File file) {
        return parseExcelFromFile(file.getAbsolutePath());
    }

    /**
     * 解析基本信息
     *
     * @param sheet   Excel工作表
     * @param request 质检报告请求
     * @throws RuntimeException 如果必要的基本信息缺失
     */
    private static void parseBasicInfo(Sheet sheet, MiTACQualityReportRequest request) {
        try {
            Map<String, String> basicInfo = new HashMap<>();
            // 用于记录缺失的必要字段
            List<String> missingFields = new ArrayList<>();

            // 根据动态检测的startRow，计算实际的数据行索引
            // 模板中的数据从startRow+1行开始，内容在第3列（索引为2）
            // 1. 公司名称 - 第一个数据行，D列单元格（由D-L列合并而成）
            String companyName = getCellValueFromMergedRegion(sheet, startRow + 1, 3); // D列单元格，行索引为startRow+1，列索引为3
            basicInfo.put("company name", companyName);

            // 2. 出货报告名称 - 第二个数据行
            String reportName = getCellValueFromMergedRegion(sheet, startRow + 2, 3); // 检查是否为合并单元格
            basicInfo.put("report name", reportName);

            // 3. 客户料号 - 第三个数据行
            String partNo = getCellValueFromMergedRegion(sheet, startRow + 3, 3); // 检查是否为合并单元格
            basicInfo.put("part no", partNo);

            // 4. Maker料号 - 第四个数据行
            String makerPartNo = getCellValueFromMergedRegion(sheet, startRow + 4, 3); // 检查是否为合并单元格
            basicInfo.put("maker part no", makerPartNo);

            // 5. 客户名称 - 第五个数据行
            String customer = getCellValueFromMergedRegion(sheet, startRow + 5, 3); // 检查是否为合并单元格
            basicInfo.put("customer", customer);

            // 6. 型号版次 - 第六个数据行
            String rev = getCellValueFromMergedRegion(sheet, startRow + 6, 3); // 检查是否为合并单元格
            basicInfo.put("rev", rev);

            // 7. 产品类别 - 第七个数据行
            String category = getCellValueFromMergedRegion(sheet, startRow + 7, 3); // 检查是否为合并单元格
            basicInfo.put("category", category);

            // 8. 品名 - 第八个数据行
            String description = getCellValueFromMergedRegion(sheet, startRow + 8, 3); // 检查是否为合并单元格
            basicInfo.put("description", description.replace("+", "&"));

            // 9. 批量 - 第九个数据行
            String lotSize = getCellValueFromMergedRegion(sheet, startRow + 9, 3); // 检查是否为合并单元格
            basicInfo.put("lot size", lotSize);

            // 10. 样本数 - 第十个和第十一个数据行
            // "尺寸"数据在E列
            // "外观"数据在E列
            String sampleSizeInner = getCellStringValue(getCell(sheet, startRow + 10, 4)); // 尺寸
            String sampleSizeOuter = getCellStringValue(getCell(sheet, startRow + 11, 4)); // 外观
            basicInfo.put("sample size inner", sampleSizeInner);
            basicInfo.put("sample size outer", sampleSizeOuter);

            // 11. 生产周别 - 第十二个数据行，因为样本数占据了两行
            String dateCode = getCellValueFromMergedRegion(sheet, startRow + 12, 3); // 检查是否为合并单元格
            basicInfo.put("date code", dateCode);

            // 12. 订单号 - 第十三个数据行
            String orderNo = getCellValueFromMergedRegion(sheet, startRow + 13, 3); // 检查是否为合并单元格
            basicInfo.put("order no", orderNo);

            // 13. ASN送货单号 - 第十四个数据行
            String asnNo = getCellValueFromMergedRegion(sheet, startRow + 14, 3); // 检查是否为合并单元格
            basicInfo.put("asn no", asnNo);

            // 调用方法设置基本信息并检查必要字段
            validateAndSetBasicInfo(basicInfo, request, missingFields);

            // 如果有缺失字段，抛出异常
            if (!missingFields.isEmpty()) {
                throw new RuntimeException("以下必要字段缺失: " + String.join(", ", missingFields));
            }

        } catch (Exception e) {
            log.error("解析基本信息异常", e);
            throw new RuntimeException("解析基本信息异常: " + e.getMessage());
        }
    }

    /**
     * 获取单元格的字符串值
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private static String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 避免科学计数法
                    double value = cell.getNumericCellValue();
                    if (value == (long) value) {
                        return String.valueOf((long) value);
                    } else {
                        return String.valueOf(value);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception ex) {
                        return cell.getCellFormula();
                    }
                }
            default:
                return "";
        }
    }

    /**
     * 获取合并单元格的值
     * 如果指定的单元格是合并单元格的一部分，则返回合并区域左上角单元格的值
     *
     * @param sheet 工作表
     * @param row   行索引
     * @param col   列索引
     * @return 单元格的值
     */
    private static String getCellValueFromMergedRegion(Sheet sheet, int row, int col) {
        // 首先尝试直接获取单元格的值
        Cell cell = getCell(sheet, row, col);
        String value = getCellStringValue(cell);

        // 如果单元格有值，直接返回
        if (value != null && !value.trim().isEmpty()) {
            return value;
        }

        // 检查该单元格是否在合并区域内
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress region = sheet.getMergedRegion(i);
            if (region.isInRange(row, col)) {
                // 获取合并区域左上角单元格的值
                Cell topLeftCell = getCell(sheet, region.getFirstRow(), region.getFirstColumn());
                return getCellStringValue(topLeftCell);
            }
        }

        // 如果不是合并单元格，返回原始值
        return value;
    }

    /**
     * 获取单元格
     *
     * @param sheet    Excel工作表
     * @param rowIndex 行索引（从0开始）
     * @param colIndex 列索引（从0开始）
     * @return 单元格
     */
    private static Cell getCell(Sheet sheet, int rowIndex, int colIndex) {
        Row row = sheet.getRow(rowIndex);
        if (row == null)
            return null;
        return row.getCell(colIndex);
    }

    /**
     * 验证并设置基本信息
     *
     * @param basicInfo     基本信息映射
     * @param request       质检报告请求
     * @param missingFields 缺失字段列表
     */
    private static void validateAndSetBasicInfo(Map<String, String> basicInfo, MiTACQualityReportRequest request,
            List<String> missingFields) {
        // 设置基本信息
        // vendor code是唯一可以有默认值的字段
        String vendorCode = basicInfo.get("vendor code");
        if (vendorCode == null || vendorCode.trim().isEmpty()) {
            vendorCode = "100518";
        }
        request.setVendorCode(vendorCode);

        // 设置样本数
        MiTACQualityReportRequest.SampleSize sampleSize = new MiTACQualityReportRequest.SampleSize();

        // 尺寸样本数在E12单元格
        String dimensionStr = basicInfo.get("sample size inner");
        if (dimensionStr == null || dimensionStr.trim().isEmpty()) {
            missingFields.add("尺寸样本数");
        } else {
            // 去除可能的非数字字符，只保留数字
            dimensionStr = dimensionStr.replaceAll("[^0-9]", "");
            if (dimensionStr.isEmpty()) {
                missingFields.add("尺寸样本数");
            } else {
                try {
                    sampleSize.setDimension(Integer.parseInt(dimensionStr));
                } catch (NumberFormatException e) {
                    missingFields.add("尺寸样本数（格式错误）");
                }
            }
        }

        // 外观样本数在E13单元格
        String appearanceStr = basicInfo.get("sample size outer");
        if (appearanceStr == null || appearanceStr.trim().isEmpty()) {
            missingFields.add("外观样本数");
        } else {
            // 去除可能的非数字字符，只保留数字
            appearanceStr = appearanceStr.replaceAll("[^0-9]", "");
            if (appearanceStr.isEmpty()) {
                missingFields.add("外观样本数");
            } else {
                try {
                    sampleSize.setAppearance(Integer.parseInt(appearanceStr));
                } catch (NumberFormatException e) {
                    missingFields.add("外观样本数（格式错误）");
                }
            }
        }

        // 记录解析结果
        request.setSampleSize(sampleSize);

        // 检查必要字段是否存在
        // 设置基本信息到请求对象，并检查是否为空
        // 公司名称
        String value = basicInfo.get("company name");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("公司名称");
        }
        request.setCompanyName(value);

        // 出货报告名称
        value = basicInfo.get("report name");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("出货报告名称");
        }
        request.setReportName(value);

        // 客户料号
        value = basicInfo.get("part no");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("客户料号");
        }
        request.setPartNo(value);

        // Maker料号
        value = basicInfo.get("maker part no");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("Maker料号");
        }
        request.setMakerPartNo(value);

        // 客户名称
        value = basicInfo.get("customer");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("客户名称");
        }
        request.setCustomer(value);

        // 型号版次
        value = basicInfo.get("rev");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("型号版次");
        }
        request.setRev(value);

        // 产品类别
        value = basicInfo.get("category");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("产品类别");
        }
        request.setCategory(value);

        // 品名
        value = basicInfo.get("description");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("品名");
        }
        request.setDescription(value);

        // 批量
        value = basicInfo.get("lot size");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("批量");
        } else {
            try {
                // 解析批量数据，例如 "1.92Kpcs" -> 1920
                Integer lotSizeValue = parseLotSize(value);
                request.setLotSize(lotSizeValue);
            } catch (Exception e) {
                log.error("解析批量数据异常: {}", value, e);
                missingFields.add("批量（格式错误）");
            }
        }

        // 生产周别
        value = basicInfo.get("date code");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("生产周别");
        }
        request.setDateCode(value);

        // 订单号
        value = basicInfo.get("order no");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("订单号");
        }
        request.setOrderNo(value);

        // ASN送货单号
        value = basicInfo.get("asn no");
        if (value == null || value.trim().isEmpty()) {
            missingFields.add("ASN送货单号");
        }
        request.setAsnNo(value);
    }

    /**
     * 解析检验项目（通用方法）
     *
     * @param sheet   Excel工作表
     * @param request 质检报告请求
     */
    private static void parseInspectionItems(Sheet sheet, MiTACQualityReportRequest request) {
        try {
            List<MiTACQualityReportRequest.Inspection> inspections = new ArrayList<>();

            // 从startRow+16的B列读取合并区域的大小，用于确定各类检验项目的读取范围
            int categoryRowIndex = startRow + 16; // 检验项目类别所在的行索引
            int categoryColIndex = 1; // B列的索引为1

            // 获取各类检验项目的合并区域大小
            // A类 - 尺寸检查（第十六个数据行）
            MiTACQualityReportRequest.Inspection inspectionA = new MiTACQualityReportRequest.Inspection();
            inspectionA.setCategory("A");
            List<MiTACQualityReportRequest.Item> itemsA = new ArrayList<>();

            // 获取A类检验项目的合并区域大小
            int aSizeRows = getMergedRegionSize(sheet, categoryRowIndex, categoryColIndex);

            // 读取A类检验项目
            // 从startRow+16开始读取，根据合并区域大小决定读取行数
            int rowA = startRow + 16;
            // i从1开始，因为有副表头
            for (int i = 1; i < aSizeRows; i++) {
                Row itemRow = sheet.getRow(rowA + i);
                if (itemRow == null)
                    continue;

                // 读取D列(索引3)的数据作为item
                String itemName = getCellStringValue(itemRow.getCell(3));
                if (itemName == null || itemName.trim().isEmpty()) {
                    log.error("A类检验项目第{}行的item为空", rowA + i);
                    throw new RuntimeException("A类检验项目明细缺失：第" + (rowA + i + 1) + "行的项目名称为空");
                }else {
                    //檢驗是否存在該檢驗項目
                    if(!InspectionItemsSet.macthInspectionItem(itemName)){
                        log.error("A类检验项目第{}行的item不存在", rowA + i);
                        throw new RuntimeException("A类检验项目不存在：" + itemName );
                    }
                }

                // 读取F列(索引5)的数据作为spec
                String spec = getCellStringValue(itemRow.getCell(5));
                if (spec == null || spec.trim().isEmpty()) {
                    log.error("A类检验项目'{}'的spec为空", itemName);
                    throw new RuntimeException("A类检验项目明细缺失：项目'" + itemName + "'的规格为空");
                }

                // 创建Item对象
                MiTACQualityReportRequest.Item item = new MiTACQualityReportRequest.Item();
                item.setItem(itemName);
                item.setSpec(spec);

                // 读取G列到N列(索引6到13)的数据，用逗号分隔拼接作为result
                StringBuilder resultBuilder = new StringBuilder();
                boolean hasResult = false;
                // 定义正则表达式
                String regex = "^[+-]?(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?$";

                for (int j = 6; j <= 130; j++) {
                    String sampleResult = getCellStringValue(itemRow.getCell(j));
                    if (sampleResult != null && !sampleResult.trim().isEmpty()) {
                        // 判断字符串是否为空并且与正则表达式匹配
                        if(Pattern.matches(regex, sampleResult)){
                            resultBuilder.append(sampleResult).append(",");
                            hasResult = true;
                        }else{
                            log.error("A类检验项目'{}'的result格式錯誤，非數值！", sampleResult);
                            throw new RuntimeException("A类检验项目的result格式錯誤，'" + sampleResult + "'非數值！");
                        }

                    }
                }

                if (!hasResult) {
                    log.error("A类检验项目'{}'的result为空", itemName);
                    throw new RuntimeException("A类检验项目明细缺失：项目'" + itemName + "'的测试结果为空");
                }

                String result = resultBuilder.toString();
                if (result.endsWith(",")) {
                    result = result.substring(0, result.length() - 1);
                }
                item.setResult(result);

                // 将Item添加到列表中
                itemsA.add(item);
            }

            inspectionA.setItems(itemsA);
            inspections.add(inspectionA);

            // 更新categoryRowIndex到B类检验项目的位置
            categoryRowIndex += aSizeRows;

            // B类 - 功能检查
            MiTACQualityReportRequest.Inspection inspectionB = new MiTACQualityReportRequest.Inspection();
            inspectionB.setCategory("B");
            List<MiTACQualityReportRequest.Item> itemsB = new ArrayList<>();

            // 获取B类检验项目的合并区域大小
            int bSizeRows = getMergedRegionSize(sheet, categoryRowIndex, categoryColIndex);

            // 读取B类检验项目
            // 从startRow+17+aSizeRows开始读取，根据合并区域大小决定读取行数
            int rowB = rowA + aSizeRows;
            // i从1开始，因为有副表头，从SizeRows-1结束，因为最后一行是空白行
            for (int i = 1; i < bSizeRows - 1; i++) {
                Row itemRow = sheet.getRow(rowB + i);
                if (itemRow == null)
                    continue;

                // 读取D列(索引3)的数据作为item
                String itemName = getCellStringValue(itemRow.getCell(3));
                if (itemName == null || itemName.trim().isEmpty()) {
                    log.error("B类检验项目第{}行的item为空", rowB + i);
                    throw new RuntimeException("B类检验项目明细缺失：第" + (rowB + i + 1) + "行的项目名称为空");
                }else {
                    //檢驗是否存在該檢驗項目
                    if(!InspectionItemsSet.macthInspectionItem(itemName)){
                        log.error("B类检验项目第{}行的item不存在", rowA + i);
                        throw new RuntimeException("B类检验项目不存在：" + itemName );
                    }
                }

                // 读取F列(索引5)的数据作为spec，由于spec在不同的产品分类可能存在或不存在，所以不需要判空
                String spec = getCellStringValue(itemRow.getCell(5));

                // 读取G列(索引6)的数据作为result
                String resultValue = getCellStringValue(itemRow.getCell(6));
                if (resultValue == null || resultValue.trim().isEmpty()) {
                    log.error("B类检验项目'{}'的result为空", itemName);
                    throw new RuntimeException("B类检验项目明细缺失：项目'" + itemName + "'的测试结果为空");
                }
                String result = getResultString(resultValue);

                // 创建Item对象
                MiTACQualityReportRequest.Item item = new MiTACQualityReportRequest.Item();
                item.setItem(itemName);
                item.setSpec(spec);
                item.setResult(result);

                // 将Item添加到列表中
                itemsB.add(item);
            }

            inspectionB.setItems(itemsB);
            inspections.add(inspectionB);

            // 更新categoryRowIndex到C类检验项目的位置
            categoryRowIndex += bSizeRows;

            // C类 - 外观检查
            MiTACQualityReportRequest.Inspection inspectionC = new MiTACQualityReportRequest.Inspection();
            inspectionC.setCategory("C");
            List<MiTACQualityReportRequest.Item> itemsC = new ArrayList<>();

            // 获取C类检验项目的合并区域大小
            int cSizeRows = getMergedRegionSize(sheet, categoryRowIndex, categoryColIndex);

            // 读取C类检验项目
            // 从startRow+17+aSizeRows+bSizeRows开始读取，根据合并区域大小决定读取行数
            int rowC = rowB + bSizeRows;
            // i从1开始，因为有副表头，从SizeRows-1结束，因为最后一行是空白行
            for (int i = 1; i < cSizeRows - 1; i++) {
                Row itemRow = sheet.getRow(rowC + i);
                if (itemRow == null)
                    continue;

                // 读取D列(索引3)的数据作为item
                String itemName = getCellStringValue(itemRow.getCell(3));
                if (itemName == null || itemName.trim().isEmpty()) {
                    log.error("C类检验项目第{}行的item为空", rowC + i);
                    throw new RuntimeException("C类检验项目明细缺失：第" + (rowC + i + 1) + "行的项目名称为空");
                }else {
                    //檢驗是否存在該檢驗項目
                    if(!InspectionItemsSet.macthInspectionItem(itemName)){
                        log.error("C类检验项目第{}行的item不存在", rowA + i);
                        throw new RuntimeException("C类检验项目不存在：" + itemName );
                    }
                }

                // 读取G列(索引6)的数据作为result
                String resultValue = getCellStringValue(itemRow.getCell(6));
                if (resultValue == null || resultValue.trim().isEmpty()) {
                    log.error("C类检验项目'{}'的result为空", itemName);
                    throw new RuntimeException("C类检验项目明细缺失：项目'" + itemName + "'的测试结果为空");
                }

                // 判断结果是 PASS 还是 NG
                // 数据内容类似“■ PASS □ NG”
                String result = getResultString(resultValue);

                // 创建Item对象
                MiTACQualityReportRequest.Item item = new MiTACQualityReportRequest.Item();
                item.setItem(itemName);
                item.setSpec(""); // C类项目没有spec数据
                item.setResult(result);

                // 将Item添加到列表中
                itemsC.add(item);
            }

            inspectionC.setItems(itemsC);
            inspections.add(inspectionC);

            // 更新categoryRowIndex到D类检验项目的位置
            categoryRowIndex += cSizeRows;

            // D类 - 包装检查
            MiTACQualityReportRequest.Inspection inspectionD = new MiTACQualityReportRequest.Inspection();
            inspectionD.setCategory("D");
            List<MiTACQualityReportRequest.Item> itemsD = new ArrayList<>();

            // 获取D类检验项目的合并区域大小
            int dSizeRows = getMergedRegionSize(sheet, categoryRowIndex, categoryColIndex);

            // 读取D类检验项目
            // 从startRow+17+aSizeRows+bSizeRows+cSizeRows开始读取，根据合并区域大小决定读取行数
            int rowD = rowC + cSizeRows;

            // 循环读取D类检验项目
            // i从1开始，因为有副表头，从SizeRows-1结束，因为最后一行是空白行
            for (int i = 1; i < dSizeRows - 1; i++) {
                Row itemRowD = sheet.getRow(rowD + i);
                if (itemRowD != null) {
                    // 读取D列(索引3)的数据作为item
                    String itemName = getCellStringValue(itemRowD.getCell(3));
                    if (itemName == null || itemName.trim().isEmpty()) {
                        log.error("D类检验项目第{}行的item为空", rowD + i);
                        continue; // 如果项目名称为空，跳过该行
                    }else {
                        //檢驗是否存在該檢驗項目
                        if(!InspectionItemsSet.macthInspectionItem(itemName)){
                            log.error("D类检验项目第{}行的item不存在", rowA + i);
                            throw new RuntimeException("D类检验项目不存在：" + itemName );
                        }
                    }

                    // 读取G列(索引6)的数据作为result
                    String resultValue = getCellStringValue(itemRowD.getCell(6));
                    if (resultValue == null || resultValue.trim().isEmpty()) {
                        log.error("D类检验项目'{}'的result为空", itemName);
                        throw new RuntimeException("D类检验项目明细缺失：项目'" + itemName + "'的测试结果为空");
                    }

                    // 判断结果是 PASS 还是 NG
                    // 数据内容类似“■ PASS □ NG”
                    String result = getResultString(resultValue);

                    // 创建Item对象
                    MiTACQualityReportRequest.Item item = new MiTACQualityReportRequest.Item();
                    item.setItem(itemName);
                    item.setSpec(""); // D类项目没有spec数据
                    item.setResult(result);

                    // 将Item添加到列表中
                    itemsD.add(item);
                }

                inspectionD.setItems(itemsD);
                inspections.add(inspectionD);

                // 更新categoryRowIndex到E类检验项目的位置
                categoryRowIndex += dSizeRows;

                // E类 - 环保要求
                MiTACQualityReportRequest.Inspection inspectionE = new MiTACQualityReportRequest.Inspection();
                inspectionE.setCategory("E");
                List<MiTACQualityReportRequest.Item> itemsE = new ArrayList<>();

                // 获取E类检验项目的合并区域大小
                int eSizeRows = getMergedRegionSize(sheet, categoryRowIndex, categoryColIndex);

                // 读取E类检验项目
                // 从startRow+17+aSizeRows+bSizeRows+cSizeRows+dSizeRows开始读取，根据合并区域大小决定读取行数
                int rowE = rowD + dSizeRows;

                // 循环读取E类检验项目
                // i从0开始，因为没有副表头，从SizeRows-1结束，因为最后一行是空白行
                for (int k = 0; k < eSizeRows - 1; k++) {
                    Row itemRowE = sheet.getRow(rowE + k);
                    if (itemRowE == null)
                        continue;
                    // 读取D列(索引3)的数据作为item
                    String itemName = getCellStringValue(itemRowE.getCell(3));
                    if (itemName == null || itemName.trim().isEmpty()) {
                        log.error("E类检验项目第{}行的item为空", rowE + k);
                        continue; // 如果项目名称为空，跳过该行
                    }else {
                        //檢驗是否存在該檢驗項目
                        if(!InspectionItemsSet.macthInspectionItem(itemName)){
                            log.error("E类检验项目第{}行的item不存在", rowA + i);
                            throw new RuntimeException("E类检验项目不存在：" + itemName );
                        }
                    }

                    // 读取G列(索引6)的数据作为result
                    String resultValue = getCellStringValue(itemRowE.getCell(6));
                    if (resultValue == null || resultValue.trim().isEmpty()) {
                        log.error("E类检验项目'{}'的result为空", itemName);
                        throw new RuntimeException("E类检验项目明细缺失：项目'" + itemName + "'的测试结果为空");
                    }

                    // 判断结果是 PASS 还是 NG
                    // 数据内容类似“■ PASS □ NG”
                    String result = getResultString(resultValue);

                    // 创建Item对象
                    MiTACQualityReportRequest.Item item = new MiTACQualityReportRequest.Item();
                    item.setItem(itemName);
                    item.setSpec(""); // E类项目没有spec数据
                    item.setResult(result);

                    // 将Item添加到列表中
                    itemsE.add(item);
                }

                inspectionE.setItems(itemsE);
                inspections.add(inspectionE);

                // 读取最终结果
                String finalResult = null; // 初始值为空
                Row resultRow = sheet.getRow(rowE + eSizeRows);
                if (resultRow != null) {
                    // 检查C列(索引2)是否包含“■”，如果是则为"OK"
                    Cell okCell = resultRow.getCell(2);
                    if (okCell != null) {
                        String okValue = getCellStringValue(okCell);
                        if (okValue != null && okValue.contains("■")) {
                            finalResult = "OK";
                        }
                    }

                    // 检查D列(索引3)是否包含“■”，如果是则为"NG"
                    Cell ngCell = resultRow.getCell(3);
                    if (ngCell != null) {
                        String ngValue = getCellStringValue(ngCell);
                        if (ngValue != null && ngValue.contains("■")) {
                            finalResult = "NG";
                        }
                    }
                }

                // 如果读取完后结果仍然为空，则抛出异常
                if (finalResult == null) {
                    log.error("最终结果未标记");
                    throw new RuntimeException("最终结果未标记，请检查第" + (startRow + 40) + "行的C列或D列是否有“■”标记");
                }

                request.setResult(finalResult);

                parseWorker(sheet, request, eSizeRows, rowE);

                request.setInspection(inspections);
            }
        } catch (Exception e) {
            log.error("解析检验项目异常", e);
            throw new RuntimeException("解析检验项目异常: " + e.getMessage());
        }
    }

    private static void parseWorker(Sheet sheet, MiTACQualityReportRequest request, int eSizeRows, int rowE) {
        // 读取审批信息
        // approvedBy在C列(索引2)，checkedBy在E列(索引4)，inspectedBy在J列(索引9)
        Row approvalRow = sheet.getRow(rowE + eSizeRows + 1);
        if (approvalRow == null) {
            log.error("审批信息行不存在，行号: {}", rowE + eSizeRows + 1);
            throw new RuntimeException("审批信息行不存在，行号: " + (rowE + eSizeRows + 1));
        }

        // 读取C列(索引2)的approvedBy
        String approvedBy = null;
        Cell approvedByCell = approvalRow.getCell(2);
        if (approvedByCell != null) {
            String value = getCellStringValue(approvedByCell);
            if (value != null && !value.trim().isEmpty()) {
                approvedBy = value.trim();
            }
        }
        if (approvedBy == null || approvedBy.isEmpty()) {
            log.error("核定人(approvedBy)不能为空");
            throw new RuntimeException("核定人(approvedBy)不能为空，请检查第" + (rowE + eSizeRows + 1) + "行的C列");
        }

        // 读取E列(索引4)的checkedBy
        String checkedBy = null;
        Cell checkedByCell = approvalRow.getCell(4);
        if (checkedByCell != null) {
            String value = getCellStringValue(checkedByCell);
            if (value != null && !value.trim().isEmpty()) {
                checkedBy = value.trim();
            }
        }
        if (checkedBy == null || checkedBy.isEmpty()) {
            log.error("复核人(checkedBy)不能为空");
            throw new RuntimeException("复核人(checkedBy)不能为空，请检查第" + (rowE + eSizeRows + 1) + "行的E列");
        }

        // inspectedBy会根据分类而在不同的列
        String category = request.getCategory().toLowerCase().replaceAll("\\s", "");
        int inspectedByCol = 20;
        if ("cpusocket".equals(category)) {
            inspectedByCol = 9;
        } else if (category.contains("cpu五金")) {
            inspectedByCol = 10;
        } else if ("cpu塑膠".equals(category) || "cpu塑胶".equals(category)) {
            inspectedByCol = 12;
        } else {
            throw new RuntimeException("未知模板類型，請檢查產品類別");
        }
        // 读取J列(索引9)的inspectedBy
        String inspectedBy = null;
        Cell inspectedByCell = approvalRow.getCell(inspectedByCol);
        if (inspectedByCell != null) {
            String value = getCellStringValue(inspectedByCell);
            if (value != null && !value.trim().isEmpty()) {
                inspectedBy = value.trim();
            }
        }
        if (inspectedBy == null || inspectedBy.isEmpty()) {
            log.error("检验人(inspectedBy)不能为空");
            throw new RuntimeException("检验人(inspectedBy)不能为空，请检查第" + (rowE + eSizeRows + 1) + "行的"+inspectedByCol+"列");
        }

        request.setApprovedBy(approvedBy);
        request.setCheckedBy(checkedBy);
        request.setInspectedBy(inspectedBy);
    }

    /**
     * 判断结果是 PASS 还是 NG
     * 
     * @param resultValue 数据内容类似“■ PASS □ NG”
     * @return PASS/NG
     */
    private static String getResultString(String resultValue) {
        if (resultValue == null || resultValue.trim().isEmpty()) {
            throw new RuntimeException("结果值不能为空");
        }

        boolean foundBox = false;
        for (int i = 0; i < resultValue.length(); i++) {
            char c = resultValue.charAt(i);

            // 找到"■"符号
            if (c == '■') {
                foundBox = true;
                // 继续往后遍历，直到找到第一个字母
                for (int j = i + 1; j < resultValue.length(); j++) {
                    char nextChar = Character.toLowerCase(resultValue.charAt(j));
                    if (Character.isLetter(nextChar)) {
                        if (nextChar == 'p') {
                            return "PASS";
                        } else if (nextChar == 'n') {
                            return "NG";
                        }
                        break; // 如果找到的字母既不是p也不是n，继续寻找下一个"■"
                    }
                }
            }
        }

        if (!foundBox) {
            throw new RuntimeException("未找到■标记");
        }
        throw new RuntimeException("未找到有效的结果标记(PASS/NG)");
    }

    /**
     * 获取指定单元格所在的合并区域的大小（行数）
     *
     * @param sheet Excel工作表
     * @param row   行索引
     * @param col   列索引
     * @return 合并区域的行数，如果不是合并单元格则返回1
     */
    private static int getMergedRegionSize(Sheet sheet, int row, int col) {
        // 检查该单元格是否在合并区域内
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress region = sheet.getMergedRegion(i);
            if (region.isInRange(row, col)) {
                // 返回合并区域的行数
                return region.getLastRow() - region.getFirstRow() + 1;
            }
        }
        // 如果不是合并单元格，返回1
        return 1;
    }

    /**
     * 解析批量数据，处理带单位的数值
     * 例如：
     * "1.92Kpcs" -> 1920
     * "500pcs" -> 500
     * "1K" -> 1000
     *
     * @param lotSizeStr 批量字符串
     * @return 解析后的整数值
     */
    private static Integer parseLotSize(String lotSizeStr) {
        if (lotSizeStr == null || lotSizeStr.trim().isEmpty()) {
            throw new RuntimeException("批量数据不能为空");
        }

        // 去除所有空格
        lotSizeStr = lotSizeStr.trim();

        // 提取数字部分和单位部分
        StringBuilder numBuilder = new StringBuilder();
        StringBuilder unitBuilder = new StringBuilder();
        boolean foundDecimalPoint = false;
        boolean foundDigit = false;

        for (int i = 0; i < lotSizeStr.length(); i++) {
            char c = lotSizeStr.charAt(i);

            // 处理数字和小数点
            if (Character.isDigit(c)) {
                numBuilder.append(c);
                foundDigit = true;
            } else if (c == '.' && !foundDecimalPoint && foundDigit) {
                numBuilder.append(c);
                foundDecimalPoint = true;
            } else if (foundDigit) {
                // 非数字部分作为单位
                unitBuilder.append(c);
            }
        }

        String numStr = numBuilder.toString();
        String unit = unitBuilder.toString().toLowerCase();

        if (numStr.isEmpty()) {
            throw new RuntimeException("批量数据格式错误：" + lotSizeStr);
        }

        // 解析数字部分
        double value;
        try {
            value = Double.parseDouble(numStr);
        } catch (NumberFormatException e) {
            throw new RuntimeException("批量数据数字部分格式错误：" + numStr);
        }

        // 根据单位进行转换
        if (unit.contains("k")) {
            // 如果单位包含 k，乘以 1000
            value *= 1000;
        }

        // 四舍五入转为整数
        return (int) Math.round(value);
    }

    /**
     * 检测Excel文件中的空白行并设置startRow
     *
     * @param sheet Excel工作表
     */
    private static void detectEmptyRowsAndSetStartRow(Sheet sheet) {
        // 由于读取数据方法中是从+1开始的，所以重置startRow为0
        startRow = 0;

        // 检查前10行是否为空白行
        for (int i = 0; i < 10; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                // 如果行为null，认为是空白行
                startRow++;
                continue;
            }

            // 检查该行是否所有单元格都为空
            boolean isEmptyRow = true;
            for (int j = 0; j < row.getLastCellNum(); j++) {
                Cell cell = row.getCell(j);
                if (cell != null) {
                    String value = getCellStringValue(cell);
                    if (value != null && !value.trim().isEmpty()) {
                        isEmptyRow = false;
                        break;
                    }
                }
            }

            if (isEmptyRow) {
                // 如果是空白行，增加startRow
                startRow++;
            } else {
                // 如果不是空白行，假设这是表头行，停止检测
                break;
            }
        }

        // 确保startRow至少为0（因为在读取数据方法中是从+1开始的）
        if (startRow < 0) {
            startRow = 0;
        }
    }
}
