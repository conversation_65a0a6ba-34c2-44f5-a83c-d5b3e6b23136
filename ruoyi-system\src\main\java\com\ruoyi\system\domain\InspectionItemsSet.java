package com.ruoyi.system.domain;

import java.util.HashSet;

/**
 * @Author: 李囯斌
 * @Date: 2025/10/9
 * @Time: 12:19
 */
public class InspectionItemsSet {

    private static final HashSet<String> a_set = new HashSet<>();
    private static final HashSet<String> b_set = new HashSet<>();
    private static final HashSet<String> c_set = new HashSet<>();
    private static final HashSet<String> d_set = new HashSet<>();
    private static final HashSet<String> e_set = new HashSet<>();

    static {
        //A類
        a_set.add("1.外框長度");
        a_set.add("2.外框寬度");
        a_set.add("3.內框長度");
        a_set.add("4.內框寬度");
        a_set.add("5.產品高度");

        a_set.add("1.總長度");
        a_set.add("2.總寬度");
        a_set.add("3.高度");

        a_set.add("2.總長度");
        a_set.add("1.ILM總長度");
        a_set.add("2.ILM總寬度");
        a_set.add("3.ILM高度");
        a_set.add("4.BP總長度");
        a_set.add("5.BP總寬度");
        a_set.add("6.BP高度");


        //B類
        b_set.add("1.LLCR測試 LLCR TEST");
        b_set.add("2.耐壓測試 Hi-pot TEST");
        b_set.add("3.錫球保持力測試 Ball Retention TEST");
        b_set.add("4.插拔耐久性測試 Durability TEST");

        b_set.add("1.實配測試");

        //C類
        c_set.add("1.鐳射內容/模印與藍圖是否一致");
        c_set.add("2.無塑膠損傷不良");
        c_set.add("3.無髒污不良");
        c_set.add("4.無塑膠起泡不良");

        c_set.add("2.無零件漏裝不良");
        c_set.add("3.無髒汙/鏽點不良");
        c_set.add("4.其他項目是否符合BKM標準");

        //D類
        d_set.add("包裝方式/數量/料號");

        //E類
        e_set.add("其它 符合\"ROHS\"環保要求");

    }

    public static boolean macthInspectionItem(String inspectionItem){
        if(a_set.contains(inspectionItem)||b_set.contains(inspectionItem)
                || c_set.contains(inspectionItem)|| d_set.contains(inspectionItem)
                || e_set.contains(inspectionItem)){
            return true;
        }
        return false;
    }
}
