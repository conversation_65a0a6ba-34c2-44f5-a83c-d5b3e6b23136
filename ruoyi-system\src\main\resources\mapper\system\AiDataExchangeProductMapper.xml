<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AiDataExchangeProductMapper">

    <resultMap type="AiDataExchangeProduct" id="AiDataExchangeProductResult">
        <result property="aiDataExchangeProductId"    column="ai_data_exchange_product_id"    />
        <result property="productName"    column="product_name"    />
    </resultMap>

    <sql id="selectAiDataExchangeProductVo">
        select ai_data_exchange_product_id, product_name from ai_data_exchange_product
    </sql>

    <select id="selectAiDataExchangeProductList" parameterType="AiDataExchangeProduct" resultMap="AiDataExchangeProductResult">
        <include refid="selectAiDataExchangeProductVo"/>
        <where>
            <if test="aiDataExchangeProductId != null "> and ai_data_exchange_product_id = #{aiDataExchangeProductId}</if>
            <if test="productName != null  and productName != ''"> and UPPER(REPLACE(product_name, ' ', '')) = #{productName}</if>
        </where>
    </select>

    <select id="selectAiDataExchangeProductByAiDataExchangeProductId" parameterType="Long" resultMap="AiDataExchangeProductResult">
        <include refid="selectAiDataExchangeProductVo"/>
        where ai_data_exchange_product_id = #{aiDataExchangeProductId}
    </select>

    <insert id="insertAiDataExchangeProduct" parameterType="AiDataExchangeProduct" useGeneratedKeys="true" keyProperty="aiDataExchangeProductId">
        insert into ai_data_exchange_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeProductId != null">ai_data_exchange_product_id,</if>
            <if test="productName != null">product_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aiDataExchangeProductId != null">#{aiDataExchangeProductId},</if>
            <if test="productName != null">#{productName},</if>
         </trim>
    </insert>

    <update id="updateAiDataExchangeProduct" parameterType="AiDataExchangeProduct">
        update ai_data_exchange_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="productName != null">product_name = #{productName},</if>
        </trim>
        where ai_data_exchange_product_id = #{aiDataExchangeProductId}
    </update>

    <delete id="deleteAiDataExchangeProductByAiDataExchangeProductId" parameterType="Long">
        delete from ai_data_exchange_product where ai_data_exchange_product_id = #{aiDataExchangeProductId}
    </delete>

    <delete id="deleteAiDataExchangeProductByAiDataExchangeProductIds" parameterType="String">
        delete from ai_data_exchange_product where ai_data_exchange_product_id in
        <foreach item="aiDataExchangeProductId" collection="array" open="(" separator="," close=")">
            #{aiDataExchangeProductId}
        </foreach>
    </delete>
</mapper>
